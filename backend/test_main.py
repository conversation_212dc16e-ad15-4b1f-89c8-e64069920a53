import unittest
import json
import os
import base64
from io import BytesIO
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

# Add project root to the Python path
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.main import app, User, current_active_user

# Mock the user dependency
def override_current_active_user():
    return User(id="test_user_id")

app.dependency_overrides[current_active_user] = override_current_active_user


class TestImageProcessor(unittest.TestCase):

    def setUp(self):
        self.client = TestClient(app)

    @patch('backend.main.db_session')
    @patch('backend.main.boto3')
    @patch('backend.main.OpenAI')
    def test_handler_calls_openai_with_correct_params(self, mock_openai, mock_boto3, mock_db_session):
        """
        Tests that the handler calls the OpenAI client with the correct parameters.
        """
        # Setup mock for the OpenAI client and its response
        mock_openai_client = MagicMock()
        mock_openai.return_value = mock_openai_client

        # Setup mock for the S3 client
        mock_s3_client = MagicMock()
        mock_s3_client.generate_presigned_url.return_value = "https://fake-url.com/image.png"
        mock_boto3.client.return_value = mock_s3_client

        # Setup mock for the db_session
        mock_db_session.return_value.__enter__.return_value = MagicMock()

        mock_response = MagicMock()
        mock_response.data = [MagicMock()]
        mock_response.data[0].b64_json = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        mock_openai_client.images.edit.return_value = mock_response

        # Set environment variables
        os.environ['OPENAI_API_KEY'] = 'fake_api_key'
        os.environ['SPACES_KEY'] = 'fake_spaces_key'
        os.environ['SPACES_SECRET'] = 'fake_spaces_secret'


        # Sample event data
        image_b64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='
        image_bytes = base64.b64decode(image_b64)

        response = self.client.post(
            "/api/jobs",
            data={"style": "Minimalist"},
            files={"image": ("test.jpg", image_bytes, "image/jpeg")}
        )

        # Assert the response from the handler
        self.assertEqual(response.status_code, 200)

        # Construct the expected prompt
        from backend.main import PROMPTS
        prompt_template = PROMPTS['Minimalist']
        if "{customizations}" not in prompt_template:
            prompt_template += ' Incorporate the following user requests if provided: "{customizations}"'
        expected_prompt = prompt_template.format(customizations='')

        # Assert that the OpenAI client was called correctly
        mock_openai_client.images.edit.assert_called_once()
        call_kwargs = mock_openai_client.images.edit.call_args.kwargs

        self.assertEqual(call_kwargs['model'], 'gpt-image-1')
        self.assertEqual(call_kwargs['prompt'], expected_prompt)
        self.assertEqual(call_kwargs['n'], 1)
        self.assertEqual(call_kwargs['size'], '1024x1024')
        self.assertEqual(call_kwargs['response_format'], 'b64_json')

        # Assert the image data passed to the API
        image_arg = call_kwargs['image']
        self.assertEqual(image_arg['bytes'], image_bytes)

        # Clean up environment variables
        del os.environ['OPENAI_API_KEY']
        del os.environ['SPACES_KEY']
        del os.environ['SPACES_SECRET']

if __name__ == '__main__':
    unittest.main()
