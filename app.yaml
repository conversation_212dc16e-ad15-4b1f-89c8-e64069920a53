name: interior-designer
services:
  - name: backend
    github:
      repo: your/repo # Replace with your repository
      branch: main
      deploy_on_push: true
    dockerfile_path: Dockerfile
    instance_size_slug: basic-xxs
    instance_count: 1
    http_port: 8080
    routes:
      - path: /api
  - name: frontend
    github:
      repo: your/repo # Replace with your repository
      branch: main
      deploy_on_push: true
    build_command: npm run build
    static_sites:
      - name: react-static
        path: /frontend/dist
    routes:
      - path: /
