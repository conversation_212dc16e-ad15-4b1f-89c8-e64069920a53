# Web Interior Designer – DigitalOcean Deployment Plan
_Based on the original AWS-centric spec_ :contentReference[oaicite:2]{index=2}

## 0  Overview
The stack is now **100 % DigitalOcean**:

| Layer | Service |
|-------|---------|
| **Frontend** | **App Platform — Static Site** (builds React from Git) |
| **Backend API** | **App Platform — Docker Image** (FastAPI) |
| **Object Storage** | **Spaces** (S3-compatible) |
| **Relational DB** | **Managed PostgreSQL** |
| **Auth** | FastAPI Users + JWT (access + refresh) |
| **LLM Image Edit** | OpenAI Images API |

All pieces live in a single DO project so your billing dashboard shows the _one_ number that matters.

---

## 1  Architecture Diagram

```mermaid
graph TD
    subgraph Browser
        A[React SPA]
    end

    subgraph DO-AppPlatform
        B[Static Site<br>(/dist)]
        C[FastAPI Container]
    end

    subgraph DigitalOcean
        D[Spaces<br>object storage]
        E[Managed PostgreSQL]
    end

    A -->|1. POST /api/jobs<br>multipart image+style| C
    C -->|2. PUT original.jpg| D
    C -->|3. Call OpenAI| F(OpenAI)
    F -->|4. redesign bytes| C
    C -->|5. PUT redesign.png| D
    C -->|6. INSERT row| E
    C -->|7. Return JSON {url...}| A
    A -->|8. GET signed URL| D
```

---

## 2  Detailed Task List

### 2.1  Repository layout
```
/frontend      React + Vite
/backend       FastAPI app
/prompt-bank   *.txt
Dockerfile
app.yaml       # App Platform spec
```

### 2.2  Frontend (React + Vite)

- [x] `UploadPhoto` → accepts image file.
- [x] `SelectStyle` → grid of cards.
- [x] `ResultCompare` → side-by-side.
- [ ] **Auth wrapper** – checks JWT in `localStorage`; redirects to `/login` if absent.
- [ ] `/login` & `/signup` forms (email + password).

Deploy:
```yaml
# app.yaml snippet
name: interior-frontend
services:
  - run_command: npm run build
    static_sites:
      - name: react-static
        github:
          branch: main
          deploy_on_push: true
          repo: your/repo
```

### 2.3  Backend (FastAPI)

#### 2.3.1  Core API routes
| Method | Path | Auth? | Purpose |
|--------|------|-------|---------|
| POST | `/auth/jwt/login` | – | Obtain tokens (`fastapi-users`) |
| POST | `/auth/jwt/refresh` | – | Rotate access token |
| POST | `/api/jobs` | ✔ | Upload image + style, start job |
| GET | `/api/jobs/{job_id}` | ✔ | Poll job status & URLs |
| GET | `/api/me` | ✔ | Return profile |

#### 2.3.2  Database schema (Alembic)

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  hashed_password TEXT NOT NULL,
  registered_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE jobs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_key TEXT NOT NULL,
  redesign_key TEXT,
  style TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  status TEXT DEFAULT 'processing'  -- processing | done | error
);
```

#### 2.3.3  `Dockerfile`

```dockerfile
FROM python:3.12-slim

ENV PYTHONUNBUFFERED=1
WORKDIR /app
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
```

#### 2.3.4  Upload + OpenAI logic (excerpt)

```python
import boto3, uuid, base64, os, httpx, sqlalchemy as sa
from fastapi import Depends, FastAPI, File, UploadFile, Form
from fastapi_users import FastAPIUsers, schemas
from openai import OpenAI

SPACES_KEY    = os.getenv("SPACES_KEY")
SPACES_SECRET = os.getenv("SPACES_SECRET")
ENDPOINT      = "https://nyc3.digitaloceanspaces.com"
BUCKET        = "interior-images"

s3 = boto3.client("s3",
                  endpoint_url=ENDPOINT,
                  aws_access_key_id=SPACES_KEY,
                  aws_secret_access_key=SPACES_SECRET)

openai = OpenAI(api_key=os.environ["OPENAI_API_KEY"])
app    = FastAPI()

@app.post("/api/jobs", response_model=JobOut)
async def create_job(
    style: str = Form(...),
    image: UploadFile = File(...),
    user: User = Depends(current_active_user),
):
    job_id = uuid.uuid4()
    key_orig = f"original/{job_id}.jpg"
    body = await image.read()
    s3.put_object(Bucket=BUCKET, Key=key_orig, Body=body, ContentType="image/jpeg")

    prompt = build_prompt(style)
    resp = openai.images.edit(
        model="gpt-image-1",
        image={"bytes": body},
        prompt=prompt,
        n=1,
        size="1024x1024",
        response_format="b64_json",
    )
    redesign_bytes = base64.b64decode(resp.data[0].b64_json)
    key_new = f"redesign/{job_id}.png"
    s3.put_object(Bucket=BUCKET, Key=key_new, Body=redesign_bytes, ContentType="image/png")

    with db_session() as db:
        db.execute(sa.text(
          "INSERT INTO jobs (id,user_id,original_key,redesign_key,style,status)"
          " VALUES (:id,:uid,:o,:r,:s,'done')"),
          dict(id=job_id, uid=user.id, o=key_orig, r=key_new, s=style))

    url = s3.generate_presigned_url("get_object",
        Params={"Bucket": BUCKET, "Key": key_new}, ExpiresIn=3600*24)
    return JobOut(id=str(job_id), url=url)
```

### 2.4  Deployment Steps (CLI)

1. **Container Registry**
   ```bash
   doctl registry login
   docker build -t registry.digitalocean.com/$PROJECT/api:$(git rev-parse --short HEAD) .
   docker push registry.digitalocean.com/$PROJECT/api:$(git rev-parse --short HEAD)
   ```
2. **Database**
   ```
   doctl databases create interior-db --engine pg --size db-s-dev-database --region nyc3
   doctl databases connection interior-db  # grab URI for INFRA_VAR
   ```
3. **Spaces bucket**
   ```
   doctl spaces create interior-images --region nyc3
   doctl spaces cdn enable interior-images
   ```
4. **Secrets & env**
   ```
   doctl apps create --spec app.yaml \
     --env FY_API=$OPENAI_KEY \
     --env DATABASE_URL=... \
     --secret SPACES_KEY=... \
     --secret SPACES_SECRET=...
   ```
5. **DNS & HTTPS** – point `app.example.com` at generated CNAME; HTTPS auto-provisions.

---

## 3  Authentication Flow

- **Registration** → `/auth/jwt/register` (FastAPI Users).
- Hash algorithm: `argon2id`.
- Access token TTL = 15 min; Refresh token TTL = 7 days.
- React stores the _access_ token in memory and the _refresh_ token in an HttpOnly cookie.

```
Browser ─► /auth/jwt/login (email, pwd)
          ◄─ tokens
Browser ─► /api/jobs     (Authorization: Bearer <access>)
If 401   ─► /auth/jwt/refresh
```

---

## 4  Cost Cheat-Sheet (May 2025, nyc3)

| Item | Qty | Price | Notes |
|------|-----|-------|-------|
| App Platform Basic, 1 container, 512 MiB | 1 | \$5 | CPU 0.5 vCPU |
| Spaces 250 GiB + 1 TiB egress | 1 | \$5 | Bundle |
| Managed PG dev-tier | 1 | \$15 | 1 vCPU / 1 GiB |
| **Subtotal ≈ \$25 / mo** | | | Similar to the AWS numbers but simpler billing. |

Billing alert e-mails at \$50, \$100, \$200 → `doctl billing alert create …`.

---

## 5  Next Milestones

1. **Move Frontend upload to presigned PUT → Spaces** (save backend RAM).
2. **WebSockets or SSE** for real-time progress bar.
3. **Lifecycle worker** to downgrade objects older than 30 days to Backblaze B2 via rclone if cost grows.
4. **Cloudflare** in front if you need WAF rules or advanced DDoS shield.

---

### You can now copy-paste this file as your canonical **DigitalOcean deployment plan** and prompt your local LLM:

> _“Follow the plan section **2 Detailed Task List** step-by-step and stop after each numbered sub-task to let me confirm.”_

