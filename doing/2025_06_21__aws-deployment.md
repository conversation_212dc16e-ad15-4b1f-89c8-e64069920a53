# Web Interior Designer – v0.4 (AWS IaC + OAuth Auth + DB + Module Skeletons)

## 1 · Project Description
This application lets users upload a photo of an interior space, choose a style, and receive an AI-generated redesign (via the OpenAI Images API).
The entire stack is declared in Terraform so an LLM coding agent can generate, validate, and apply infra from the terminal.

## 2 · Goals and Checkpoints
* **CP-0 Bootstrap** – Remote state (S3 + DynamoDB lock) in `us-west-2`, AWS Budget **USD 10/mo**
* **CP-1 “Hello World”** – Lambda + HTTP API, Amplify hosts React
* **CP-2A Image Bucket** – Versioned S3 bucket + least-privilege IAM
* **CP-2B Jobs Table** – DynamoDB `jobs` with `job_id`, `created_at`, `user_id`, `style`, `status`, `retry_count`, `expires_at`
* **CP-3A OAuth Auth** – Cognito User/Identity pools, **Google | Facebook | Apple**, no guests
* **CP-3B Auth-Aware API** – JWT authoriser, all endpoints require sign-in
* **CP-4 Image Lambda** – Calls OpenAI, stores redesign, updates DynamoDB
* **CP-5 CI/CD** – GitHub Actions: validate, tflint, tfsec, plan

## 3 · Out of Scope
No custom domain/CDN, no advanced cost-optimisation, no multi-region.

## 4 · Architecture (text)
1. React bundle is served from Amplify Hosting.
2. User authenticates via Cognito hosted-UI (Google/Facebook/Apple).
3. Browser calls API Gateway with JWT → router Lambda.
4. Lambda issues pre-signed S3 URL, stores job row in DynamoDB.
5. Image Lambda downloads original, calls OpenAI, uploads redesign, updates job row.

## 5 · Terraform Layout
```
infra/
  main.tf  variables.tf  providers.tf  versions.tf
  modules/
    budgets/
    dynamodb_table/
    oauth_cognito/
```

## 6 · Milestone Details & Validation

### CP-0
```
aws budgets describe-budgets --query 'Budgets[0].BudgetLimit.Amount'  # → "10"
```

### CP-1
```
curl $(terraform output -raw hello_api_url)  # → {"message":"hello"}
```

### CP-2A
```
aws s3api get-bucket-versioning --bucket $IMG_BUCKET
```

### CP-2B
```
aws dynamodb describe-table --table-name jobs
```

### CP-3A
* Complete OAuth in hosted UI, then:
```
aws cognito-idp list-users-in-user-pool --user-pool-id <pool> --query 'Users[].UserStatus'  # → EXTERNAL_PROVIDER
```

### CP-3B
```
TOKEN=$(aws cognito-idp initiate-auth ... --query 'AuthenticationResult.IdToken' --output text)
curl -H "Authorization:$TOKEN" $(terraform output -raw hello_api_url)
```

### CP-4
```
aws s3 cp sample.jpg s3://$IMG_BUCKET/originals/$(uuidgen).jpg
```

### CP-5 – GitHub Actions snippet
```yaml
jobs:
  terraform:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: hashicorp/setup-terraform@v3
      - run: terraform init -input=false
      - run: terraform validate
      - run: terraform plan -no-color
      - run: tflint --enable-rule=aws_*
      - run: tfsec .
```

## 7 · LLM-Agent Playbook
* `terraform fmt -recursive`
* `tflint && tfsec .`
* `aws budgets describe-budgets`  → 10
* `aws iam get-role --role-name cognito_unauth_role` (fail)
* `aws dynamodb scan --table-name jobs --limit 1`

## 8 · Frontend Auth Wiring
```
npm i aws-amplify @aws-amplify/ui-react
```
```js
import { Amplify } from 'aws-amplify';
import awsExports from './aws-exports';
Amplify.configure(awsExports);
```

## 9 · Open Items
* Provide OAuth client IDs/secrets (SSM or repo secrets)
* Decide max `retry_count`
* Extra monitoring/alerts

## 10 · Module Skeletons

### modules/budgets/main.tf
```
resource "aws_budgets_budget" "monthly" {
  name              = var.name
  budget_type       = "COST"
  limit_amount      = var.monthly_limit
  limit_unit        = "USD"
  time_unit         = "MONTHLY"

  notification {
    comparison_operator = "GREATER_THAN"
    threshold           = 100
    threshold_type      = "PERCENTAGE"
    notification_type   = "ACTUAL"

    subscriber {
      subscription_type = "EMAIL"
      address           = var.alert_email
    }
  }
}
```

### modules/budgets/variables.tf
```
variable "name"         { default = "monthly-budget" }
variable "monthly_limit" { default = "10" }
variable "alert_email"  { type = string }
```

### modules/budgets/outputs.tf
```
output "budget_arn" { value = aws_budgets_budget.monthly.arn }
```

### modules/dynamodb_table/main.tf
```
resource "aws_dynamodb_table" "this" {
  name         = var.table_name
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "job_id"
  range_key    = "created_at"

  attribute { name = "job_id"     type = "S" }
  attribute { name = "created_at" type = "S" }

  dynamic "attribute" {
    for_each = var.extra_attributes
    content {
      name = attribute.value.name
      type = attribute.value.type
    }
  }

  ttl {
    attribute_name = "expires_at"
    enabled        = true
  }
}
```

### modules/dynamodb_table/variables.tf
```
variable "table_name" { default = "jobs" }

variable "extra_attributes" {
  type = list(object({ name = string, type = string }))
  default = [
    { name = "status",      type = "S" },
    { name = "user_id",     type = "S" },
    { name = "style",       type = "S" },
    { name = "retry_count", type = "N" },
    { name = "expires_at",  type = "N" }
  ]
}
```

### modules/dynamodb_table/outputs.tf
```
output "table_name" { value = aws_dynamodb_table.this.name }
```

### modules/oauth_cognito/main.tf
```
resource "aws_cognito_user_pool" "main" {
  name = var.pool_name
  username_attributes = ["email"]
  mfa_configuration   = "OFF"
  password_policy {
    minimum_length = 8
  }
}

resource "aws_cognito_user_pool_domain" "this" {
  domain       = var.domain_prefix
  user_pool_id = aws_cognito_user_pool.main.id
}

resource "aws_cognito_identity_provider" "google" {
  provider_type = "Google"
  user_pool_id  = aws_cognito_user_pool.main.id
  provider_name = "Google"
  attribute_mapping = { email = "email", sub = "sub" }
  provider_details  = {
    client_id     = var.google_client_id
    client_secret = var.google_client_secret
    authorize_scopes = "profile email openid"
  }
}

# TODO – add Facebook & Apple providers

resource "aws_cognito_user_pool_client" "app" {
  name            = "${var.pool_name}-client"
  user_pool_id    = aws_cognito_user_pool.main.id
  generate_secret = false
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_flows  = ["code"]
  allowed_oauth_scopes = ["email", "openid", "profile"]
  supported_identity_providers = ["Google", "Facebook", "SignInWithApple"]
  callback_urls = var.callback_urls
  logout_urls   = var.logout_urls
}

resource "aws_cognito_identity_pool" "main" {
  identity_pool_name               = "${var.pool_name}-identity"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id     = aws_cognito_user_pool_client.app.id
    provider_name = aws_cognito_user_pool.main.endpoint
    server_side_token_check = true
  }
}

# TODO – IAM role for authenticated users
```

### modules/oauth_cognito/variables.tf
```
variable "pool_name"      { default = "oauth-user-pool" }
variable "domain_prefix"  { default = "interior-designer-oauth" }

variable "google_client_id"     { type = string }
variable "google_client_secret" { type = string }

variable "facebook_app_id"      { type = string }
variable "facebook_app_secret"  { type = string }

variable "apple_client_id"          { type = string }
variable "apple_team_id"            { type = string }
variable "apple_private_key_id"     { type = string }
variable "apple_private_key"        { type = string }

variable "callback_urls" { type = list(string) default = ["http://localhost:3000/"] }
variable "logout_urls"   { type = list(string) default = ["http://localhost:3000/"] }
```

### modules/oauth_cognito/outputs.tf
```
output "user_pool_id"        { value = aws_cognito_user_pool.main.id }
output "user_pool_client_id" { value = aws_cognito_user_pool_client.app.id }
output "identity_pool_id"    { value = aws_cognito_identity_pool.main.id }
output "hosted_ui_url" {
  value = "https://${aws_cognito_user_pool_domain.this.domain}.auth.${data.aws_region.current.name}.amazoncognito.com/login?client_id=${aws_cognito_user_pool_client.app.id}&response_type=code&scope=email+openid+profile&redirect_uri=${var.callback_urls[0]}"
}
```

## 11 · Agent Implementation Steps
1. `cd infra`
2. Export OAuth secrets (vars or SSM)
3. `terraform init -upgrade`
4. `terraform validate`
5. `terraform fmt -recursive && tflint && tfsec .`
6. `terraform plan -target=module.budgets`
7. Fill TODOs, apply modules incrementally, run validation commands.

## 12 · Revision History
* v0.1 2025-05-03 – prototype
* v0.2 2025-06-21 – AWS stack, Cognito, DynamoDB, budget 50
* v0.3 2025-06-21 – OAuth-only, no guest, budget 10
* v0.4 2025-06-21 – adds Terraform skeletons
