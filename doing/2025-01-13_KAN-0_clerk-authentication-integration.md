# Clerk.com Authentication Integration

**Date:** 2025-01-13  
**Ticket:** CLERK-AUTH  
**Topic:** Clerk Authentication Integration  

## 1. Overview

This document tracks the integration of Clerk.com authentication into the Web Interior Designer application to support:
- Google OAuth
- Apple Sign-In (via browser/PWA)
- Passwordless email authentication (magic links)

## 2. Current State

The application currently has:
- React + Vite frontend with Chakra UI
- FastAPI backend
- No authentication system implemented
- Planning documents mention JWT-based auth but not implemented

## 3. Goals

### Primary Goals
- [ ] Integrate Clerk.com for user authentication
- [ ] Support Google OAuth login/signup
- [ ] Support Apple Sign-In for browser/PWA
- [ ] Support passwordless email authentication
- [ ] Protect main app functionality behind authentication
- [ ] Associate user designs with authenticated users

### Secondary Goals
- [ ] Maintain existing UI/UX flow
- [ ] Add user profile management
- [ ] Prepare for future user-specific features (saved designs, history)

## 4. Technical Implementation Plan

### 4.1 Frontend Changes (React + Vite)

#### Dependencies
```bash
npm install @clerk/clerk-react
```

#### Environment Variables
```env
VITE_CLERK_PUBLISHABLE_KEY=pk_test_...
```

#### Core Components
- `ClerkProvider` wrapper in main.tsx
- `SignIn` component for authentication
- `SignUp` component for registration
- `UserProfile` component for user management
- `ProtectedRoute` wrapper for authenticated routes

#### Routing Structure
```
/                    → Protected main app (existing functionality)
/sign-in            → Clerk SignIn component
/sign-up            → Clerk SignUp component  
/profile            → User profile management
```

### 4.2 Backend Changes (FastAPI)

#### Dependencies
```bash
pip install pyjwt cryptography requests
```

#### Authentication Middleware
- JWT token validation middleware
- Clerk public key fetching and caching
- User context injection for protected endpoints

#### Protected Endpoints
- All existing API endpoints will require authentication
- User ID will be extracted from JWT and used for data association

### 4.3 Clerk Dashboard Configuration

#### Authentication Methods
- Google OAuth (requires Google Cloud Console setup)
- Apple Sign-In (requires Apple Developer setup)
- Email passwordless (magic links)

#### Application Settings
- Allowed redirect URLs for development and production
- Session management configuration
- User profile fields configuration

## 5. Implementation Steps

### Phase 1: Basic Setup
1. [ ] Create Clerk.com account and application
2. [ ] Install frontend dependencies
3. [ ] Set up ClerkProvider in React app
4. [ ] Create basic sign-in/sign-up pages

### Phase 2: Authentication Flow
5. [ ] Implement protected routes
6. [ ] Create authentication wrapper for main app
7. [ ] Add sign-out functionality
8. [ ] Test basic email authentication

### Phase 3: OAuth Integration
9. [ ] Configure Google OAuth in Clerk dashboard
10. [ ] Configure Apple Sign-In in Clerk dashboard
11. [ ] Test OAuth flows

### Phase 4: Backend Integration
12. [ ] Add JWT validation to FastAPI
13. [ ] Update API endpoints to use user context
14. [ ] Test end-to-end authentication

### Phase 5: Testing & Validation
15. [ ] Write authentication tests
16. [ ] Test all authentication methods
17. [ ] Validate user experience flows

## 6. Environment Configuration

### Development
- Clerk test environment
- Local development URLs for redirects
- Test OAuth credentials

### Production
- Clerk production environment
- Production domain URLs
- Production OAuth credentials

## 7. Security Considerations

- JWT token validation on all protected endpoints
- Secure storage of Clerk secrets
- HTTPS enforcement for OAuth redirects
- Session management and token refresh

## 8. Testing Strategy

### Manual Testing
- [ ] Google OAuth sign-in/sign-up
- [ ] Apple Sign-In (requires Safari/iOS testing)
- [ ] Email magic link authentication
- [ ] Sign-out and session management
- [ ] Protected route access

### Automated Testing
- [ ] Authentication component tests
- [ ] Protected route tests
- [ ] JWT validation tests
- [ ] API endpoint authentication tests

## 9. Validation Checklist

- [ ] All authentication methods work correctly
- [ ] Protected routes properly redirect unauthenticated users
- [ ] User context is properly passed to backend
- [ ] Existing app functionality works with authentication
- [ ] No authentication bypasses or security issues

## 10. Deployment Notes

- Environment variables must be set in production
- OAuth redirect URLs must be updated for production domain
- Clerk webhook endpoints may be needed for user management

## 11. Progress Log

### 2025-01-13
- [ ] Created planning document
- [ ] Created Jira ticket content
- [ ] Started implementation planning

---

**Next Steps:** Begin with Clerk.com account setup and basic frontend integration.
