# Web Interior Designer Application Specification (Initial Phase)

## Project Description
This document outlines the plan for the initial phase of building a web application that allows users to upload images of interior spaces and receive AI-generated redesigns based on selected styles. The application will utilize the OpenAI image API for image processing and will be deployed on Amazon Web Services.

## Goals (Initial Phase)
The primary goal of this initial phase is to implement the core workflow of the application, enabling users to:
1.  Provide an image of an interior space.
2.  Select a design style to be applied.
3.  View the AI-generated redesigned image based on the selected style.

**Checkpoint 1 (Local):** Be able to run an image processing via the OpenAI image API, with the image provided by the frontend and processed by a local backend process.

## Out of Scope for this Phase
The following features and functionalities are explicitly excluded from this initial development phase:
*   User Authentication and authorization.
*   Persistent storage of generated renders for later user review (beyond temporary storage for display).
*   Advanced error handling, input validation, and edge case management.
*   Specific UI/UX considerations tailored solely for desktop environments (mobile-first approach is prioritized).

## Architecture Overview

```mermaid
graph TD
    A[User Browser] -->|Upload Image & Style| B(AWS Amplify Frontend)
    B -->|Invoke Lambda Function| C(AWS API Gateway)
    C -->|Trigger Lambda Function| D(AWS Lambda Function: Process Image)
    D -->|Call OpenAI API| E(OpenAI Image API)
    E -->|Return Redesigned Image| D
    D -->|Store Images| F(Amazon S3)
    F -->|Retrieve Redesigned Image| D
    D -->|Return Redesigned Image URL| B
    B -->|Display Redesigned Image| A
```

## Detailed Steps for Implementation

### 1. Frontend Setup (AWS Amplify)
*   [x] Initialize a new AWS Amplify project in the project directory.
*   [x] Configure Amplify to host a single-page web application.
*   [x] Set up a frontend framework (e.g., React, Vue, Angular - planning assumes React, adjust as needed).
*   [x] Create the "Upload Photo" component with a file input and a "Continue" button.
*   [x] Create the "Select Style" component displaying selectable design style options and an "Apply Style" button.
*   [x] Create the "View & Save" component to display the original and redesigned images.

### 2. Backend Setup (AWS Lambda & API Gateway)
*   [ ] Create a new AWS Lambda function using Python.
*   [ ] Configure an API Gateway endpoint (HTTP POST) to trigger the Lambda function.
*   [ ] Set up necessary IAM roles and permissions for Lambda to interact with S3 and OpenAI.

### 3. Image Storage Setup (Amazon S3)
*   [ ] Create a new S3 bucket for storing original and redesigned images.
*   [ ] Configure bucket policies for read/write access from the Lambda function.

### 4. Implement Core Logic
*   **Frontend:**
    *   [x] Handle image file selection and prepare for upload.
    *   [x] Capture the selected design style.
    *   [x] Send image data and style to the API Gateway endpoint.
    *   [x] Receive the S3 URL of the redesigned image from the backend.
    *   [x] Display original and redesigned images in the "View & Save" component.
*   **Backend (Lambda Function):**
    *   [x] **Dry-Run Mode:** Check for a `DRY_RUN` environment variable. If `true`, log the prompt and image info, then return a mock response without calling the OpenAI API.
    *   [ ] **Prompt Management:**
        *   [x] Store detailed prompts for each design style in a separate text file.
        *   [x] Load prompts from the `prompts/` directory at runtime.
        *   [x] Select the appropriate prompt based on the `style` from the request.
        *   [x] Integrate any `customizations` from the user into the selected prompt.
    *   [ ] Receive image data, style, and customizations from the API Gateway event.
    *   [ ] Upload the original image to the S3 bucket.
    *   [ ] Call the OpenAI image *edit* API with the original image and the dynamically constructed prompt. (Refer to: https://platform.openai.com/docs/guides/image-generation?image-generation-model=gpt-image-1&lang=python)
    *   [ ] Implement error handling for the OpenAI API call.
    *   [ ] Receive the redesigned image from the OpenAI API response.
    *   [ ] Upload the redesigned image to the S3 bucket.
    *   [ ] Generate a pre-signed URL for the redesigned image in S3 (or use public read access).
    *   [ ] Return the S3 URL of the redesigned image in the API Gateway response.

### 5. Refinement and Testing
*   [ ] Test the complete workflow: upload, style selection, and result display.
*   [ ] Refine UI/UX, prioritizing mobile responsiveness.

### 6. Prompt Management and Dry-Run Mode

#### Prompt Strategy
To ensure high-quality, style-consistent results, we will use detailed prompts for each design style. These prompts, located in the `prompts/` directory, will guide the AI to produce transformations that align with the specific aesthetic while maintaining the original photo's layout.

#### Backend Logic for Dry-Run and Prompts

```mermaid
graph TD
    subgraph Backend Lambda
        A[Receive Request (Image, Style, Customizations)] --> B{DRY_RUN enabled?};
        B -->|Yes| C[Log Prompt and Image Info];
        C --> D[Return Mock Response];
        B -->|No| E[Select Prompt based on Style];
        E --> F[Append User Customizations];
        F --> G[Call OpenAI API];
        G --> H[Process Response];
        H --> I[Return Redesigned Image];
    end
    I --> J[Frontend];
    D --> J;
```

#### Dry-Run Mode
For development and testing, a `DRY_RUN` mode will be implemented in the backend. When enabled via an environment variable (`DRY_RUN=true`), the Lambda function will:
1.  Construct the full prompt as it would for a real request.
2.  Log the prompt and the path of the image that would be processed.
3.  Bypass the OpenAI API call.
4.  Return a mock response, preventing actual API costs.

This allows for rapid testing of the prompt generation logic without incurring charges.

## Reasoning Behind Decisions

*   **AWS Amplify for Frontend:** Chosen for its ease of deployment, hosting, and integration with other AWS services like API Gateway and Lambda. It simplifies the process of getting the frontend application online and connected to the backend.
*   **Python AWS Lambda for Backend:** Python is a versatile language with strong libraries for image processing and API interactions (like with OpenAI). Lambda provides a serverless, scalable, and cost-effective solution for running the backend logic without managing servers.
*   **Amazon S3 for Image Storage:** S3 is a highly durable, scalable, and cost-effective object storage service. It's a standard choice for storing large amounts of data like images and integrates well with AWS Lambda.
*   **OpenAI Image API:** Selected as the core engine for image redesign based on the project requirements.
*   **Mobile-First Approach:** Prioritizing mobile development aligns with current web usage trends and ensures a good user experience on smaller screens before optimizing for larger ones.
*   **Excluding Authentication Initially:** Focusing on the core image processing workflow first allows for faster iteration and validation of the central functionality before adding complexity like user management.

## Mockup Images
Mockup images illustrating the user interface and workflow will be attached separately to provide visual context for this specification.
