# Supabase Integration Specification
## TanStack React Frontend + FastAPI Backend

## 🚀 PROGRESS UPDATE - 2025-07-13
**Phase 1 & 2 Frontend Integration: ✅ COMPLETED**
**Frontend Error Fixes: ✅ COMPLETED**

### ✅ Completed Tasks:
1. **Environment Variables Setup** - Updated .env with correct VITE_ prefixes
2. **Supabase Dependencies** - Installed @supabase/supabase-js successfully
3. **Supabase Client** - Created src/lib/supabase.js with proper configuration
4. **Authentication Context** - Created src/context/AuthContext.jsx with full auth state management
5. **Main App Integration** - Updated src/main.tsx with AuthProvider wrapper
6. **Authentication Components** - Created LoginForm and ProtectedRoute components
7. **Route Integration** - Updated main.tsx with login/dashboard routes and navigation
8. **Development Server** - Successfully running at http://localhost:3000
9. **Chakra UI v3 Compatibility** - Fixed AlertIcon import errors in LoginForm component
10. **Alert Components** - Updated to use Alert.Root, Alert.Indicator, and Alert.Title syntax
11. **Form Controls** - Replaced FormControl/FormLabel with Box/Text for Chakra UI v3 compatibility

### 🎯 Current Status:
- Frontend authentication system is fully implemented and running
- Development server is operational without errors (AlertIcon import issue resolved)
- All Chakra UI components are using v3 syntax correctly
- **Blank page issue resolved** - Router structure fixed to prevent useAuth() context errors
- Basic page rendering confirmed working
- Environment variables are properly configured
- Ready for testing with actual Supabase authentication

### 🔧 Technical Fixes Applied:
- **Import Error Resolution**: Removed non-existent `AlertIcon` and `AlertDescription` imports
- **Alert Component Updates**: Migrated to Chakra UI v3 Alert.Root/Alert.Indicator/Alert.Title pattern
- **Form Styling**: Replaced deprecated FormControl components with Box/Text combinations
- **Router Context Issue**: Fixed useAuth() being called outside AuthProvider context
- **Component Structure**: Simplified RootComponent to prevent context access errors
- **HMR Stability**: Resolved Fast Refresh issues causing development instability
- **Validation**: Confirmed no TypeScript/ESLint errors in updated components

### 📋 Next Steps:
- Test authentication flow with real user signup/login
- Implement backend FastAPI integration (Phase 3)
- Set up database schema and RLS policies (Phase 4)

### Overview
This specification outlines the integration of Supabase authentication and database into an existing TanStack React application with FastAPI backend. The integration follows modern best practices for secure authentication and database management.

### Prerequisites
- Existing TanStack React app with TanStack Router
- FastAPI backend server
- Supabase project created at https://supabase.com

## Phase 1: Supabase Project Setup

### 1.1 Create Supabase Project
1. Go to https://supabase.com and create a new project
2. Note down:
   - Project URL (REACT_APP_SUPABASE_URL)
   - Project API Key (anon/public) (REACT_APP_SUPABASE_ANON_KEY)
   - JWT Secret (for backend verification)

### 1.2 Environment Variables ✅ COMPLETED
Create/update `.env` files:

**Frontend (.env)** ✅ COMPLETED
```
VITE_SUPABASE_URL=https://oxhrxvbykjbgwkliasmn.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im94aHJ4dmJ5a2piZ3drbGlhc21uIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MTY0MjUsImV4cCI6MjA2Nzk5MjQyNX0.sHVuLVvAi-KeTfH8vwv23-Y1zd5OlZfcK7670ATdb9s
```

**Backend (.env)**
```
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_JWT_SECRET=your_jwt_secret
```

## Phase 2: Frontend Integration (React + TanStack Router) ✅ COMPLETED

### 2.1 Install Dependencies ✅ COMPLETED
```bash
npm install @supabase/supabase-js @supabase/auth-ui-react @supabase/auth-ui-shared
```

### 2.2 Create Supabase Client ✅ COMPLETED
Create `src/lib/supabase.js`:
```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### 2.3 Create Authentication Context ✅ COMPLETED
Create `src/context/AuthContext.jsx`:
```javascript
import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '../lib/supabase'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [session, setSession] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  }

  const signUp = async (email, password) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { data, error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const value = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
```

### 2.4 Update Main App Entry Point ✅ COMPLETED
Update `src/main.tsx` to include AuthProvider:
```typescript
import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import { RouterProvider, createRouter } from '@tanstack/react-router'
import { AuthProvider } from './context/AuthContext'

// Import the generated route tree
import { routeTree } from './routeTree.gen'

import './styles.css'

// Create router instance
const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0,
  scrollRestoration: true,
  defaultStructuralSharing: true,
})

// Register router for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('app')!
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <AuthProvider>
        <RouterProvider router={router} />
      </AuthProvider>
    </StrictMode>
  )
}
```

### 2.5 Create Authentication Components ✅ COMPLETED
Create `src/components/LoginForm.jsx` and `src/components/ProtectedRoute.jsx`:
```javascript
import { useState } from 'react'
import { useAuth } from '../../context/AuthContext'
import { useNavigate } from '@tanstack/react-router'

export const LoginForm = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const { signIn } = useAuth()
  const navigate = useNavigate()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { error } = await signIn(email, password)

    if (error) {
      setError(error.message)
    } else {
      navigate({ to: '/dashboard' })
    }

    setLoading(false)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-md mx-auto">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
          Email
        </label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        />
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
          Password
        </label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        />
      </div>

      <button
        type="submit"
        disabled={loading}
        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
      >
        {loading ? 'Signing in...' : 'Sign In'}
      </button>
    </form>
  )
}
```

### 2.6 Create Protected Route Component
Create `src/components/ProtectedRoute.jsx`:
```javascript
import { useAuth } from '../context/AuthContext'
import { Navigate } from '@tanstack/react-router'

export const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>
  }

  if (!user) {
    return <Navigate to="/login" />
  }

  return children
}
```

### 2.7 Update Root Route with Navigation
Update `src/routes/__root.tsx`:
```typescript
import { Outlet, createRootRoute, Link } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { useAuth } from '../context/AuthContext'

export const Route = createRootRoute({
  component: () => {
    const { user, signOut } = useAuth()

    return (
      <>
        <header className="bg-white shadow">
          <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <Link to="/" className="text-xl font-bold text-gray-900">
                  My App
                </Link>
              </div>
              <div className="flex items-center space-x-4">
                {user ? (
                  <>
                    <Link to="/dashboard" className="text-gray-700 hover:text-gray-900">
                      Dashboard
                    </Link>
                    <span className="text-gray-500">{user.email}</span>
                    <button
                      onClick={signOut}
                      className="text-gray-700 hover:text-gray-900"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link to="/login" className="text-gray-700 hover:text-gray-900">
                      Login
                    </Link>
                    <Link to="/signup" className="text-gray-700 hover:text-gray-900">
                      Sign Up
                    </Link>
                  </>
                )}
              </div>
            </div>
          </nav>
        </header>
        <main>
          <Outlet />
        </main>
        <TanStackRouterDevtools />
      </>
    )
  },
})
```

### 2.8 Create Authentication Routes
Create `src/routes/login.tsx`:
```typescript
import { createFileRoute } from '@tanstack/react-router'
import { LoginForm } from '../components/auth/LoginForm'

export const Route = createFileRoute('/login')({
  component: () => (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
        </div>
        <LoginForm />
      </div>
    </div>
  ),
})
```

Create `src/routes/dashboard.tsx`:
```typescript
import { createFileRoute } from '@tanstack/react-router'
import { ProtectedRoute } from '../components/ProtectedRoute'
import { useAuth } from '../context/AuthContext'

export const Route = createFileRoute('/dashboard')({
  component: () => (
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  ),
})

function Dashboard() {
  const { user } = useAuth()

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 py-6 sm:px-0">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-2 text-gray-600">Welcome, {user?.email}!</p>
      </div>
    </div>
  )
}
```

## Phase 3: Backend Integration (FastAPI)

### 3.1 Install Python Dependencies
```bash
pip install fastapi uvicorn python-jose python-multipart supabase python-dotenv
```

### 3.2 Create Supabase Client
Create `backend/lib/supabase.py`:
```python
import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)
```

### 3.3 Create Authentication Dependencies
Create `backend/auth/dependencies.py`:
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from typing import Optional
import os
from supabase import Client
from .supabase import supabase

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Verify JWT token and return user information
    """
    token = credentials.credentials

    try:
        # Verify JWT token using Supabase JWT secret
        jwt_secret = os.environ.get("SUPABASE_JWT_SECRET")
        payload = jwt.decode(token, jwt_secret, algorithms=["HS256"])

        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Get user from Supabase
        response = supabase.auth.get_user(token)
        if not response.user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return response.user

    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_active_user(current_user = Depends(get_current_user)):
    """
    Ensure user is active
    """
    if not current_user.email_confirmed_at:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email not confirmed"
        )
    return current_user
```

### 3.4 Create FastAPI Routes
Create `backend/routers/auth.py`:
```python
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from ..auth.dependencies import get_current_active_user
from ..lib.supabase import supabase

router = APIRouter(prefix="/auth", tags=["authentication"])

class UserResponse(BaseModel):
    id: str
    email: str
    created_at: str

@router.get("/me", response_model=UserResponse)
async def get_me(current_user = Depends(get_current_active_user)):
    """Get current user information"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        created_at=current_user.created_at
    )

@router.post("/refresh")
async def refresh_token(current_user = Depends(get_current_active_user)):
    """Refresh JWT token"""
    # This would typically be handled by the frontend
    # But you can implement token refresh logic here if needed
    return {"message": "Token is valid"}
```

### 3.5 Create Protected API Routes
Create `backend/routers/protected.py`:
```python
from fastapi import APIRouter, Depends
from ..auth.dependencies import get_current_active_user
from ..lib.supabase import supabase

router = APIRouter(prefix="/protected", tags=["protected"])

@router.get("/data")
async def get_protected_data(current_user = Depends(get_current_active_user)):
    """Example protected endpoint"""
    return {
        "message": "This is protected data",
        "user_id": current_user.id,
        "user_email": current_user.email
    }

@router.get("/user-data")
async def get_user_data(current_user = Depends(get_current_active_user)):
    """Get user-specific data from database"""
    # Example: Query user-specific data
    response = supabase.table("user_profiles").select("*").eq("user_id", current_user.id).execute()
    return {"data": response.data}
```

### 3.6 Update Main FastAPI Application
Update `backend/main.py`:
```python
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from .routers import auth, protected
from .auth.dependencies import get_current_active_user

app = FastAPI(title="My App API", description="API with Supabase authentication")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(protected.router)

@app.get("/")
async def root():
    return {"message": "API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
```

## Phase 4: Database Schema Setup

### 4.1 Create Database Tables
In Supabase SQL Editor, create basic user profile table:
```sql
-- Create user profiles table
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for user profiles
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (id, user_id, full_name)
    VALUES (NEW.id, NEW.id, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

### 4.2 Create API Client Service
Create `src/services/api.js`:
```javascript
import { supabase } from '../lib/supabase'

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000'

class ApiClient {
  async request(endpoint, options = {}) {
    const { data: { session } } = await supabase.auth.getSession()

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    if (session?.access_token) {
      config.headers.Authorization = `Bearer ${session.access_token}`
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config)

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }

    return response.json()
  }

  async get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'GET' })
  }

  async post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'DELETE' })
  }
}

export const apiClient = new ApiClient()
```

## Phase 5: Testing & Deployment Considerations

### 5.1 Environment Setup
- Set up proper environment variables for different stages (development, staging, production)
- Configure CORS properly for production domains
- Set up proper JWT secret management

### 5.2 Security Considerations
- Implement proper Row Level Security (RLS) policies in Supabase
- Use service role key only on backend, never expose it to frontend
- Implement proper error handling to avoid information leakage
- Consider implementing rate limiting

### 5.3 Testing Strategy
- Create unit tests for authentication flow
- Test protected routes with valid/invalid tokens
- Test database operations with proper user context
- Implement integration tests for full authentication flow

## Implementation Order

1. **Phase 1**: Set up Supabase project and environment variables
2. **Phase 2**: Implement frontend authentication (context, components, routes)
3. **Phase 3**: Implement backend authentication (dependencies, routes)
4. **Phase 4**: Set up database schema and RLS policies
5. **Phase 5**: Test integration and implement proper error handling

## Key Files to Create/Modify

### Frontend
- `src/lib/supabase.js` - Supabase client
- `src/context/AuthContext.jsx` - Authentication context
- `src/components/auth/LoginForm.jsx` - Login form
- `src/components/ProtectedRoute.jsx` - Route protection
- `src/routes/login.tsx` - Login route
- `src/routes/dashboard.tsx` - Protected dashboard
- `src/services/api.js` - API client
- `src/main.tsx` - Updated with AuthProvider

### Backend
- `backend/lib/supabase.py` - Supabase client
- `backend/auth/dependencies.py` - Authentication dependencies
- `backend/routers/auth.py` - Authentication routes
- `backend/routers/protected.py` - Protected routes
- `backend/main.py` - Updated FastAPI app

This specification provides a complete roadmap for integrating Supabase authentication and database into your TanStack React + FastAPI application, following modern security practices and maintainable code patterns.
