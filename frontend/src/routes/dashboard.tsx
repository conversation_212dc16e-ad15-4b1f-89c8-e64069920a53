import { createFileRoute, redirect } from '@tanstack/react-router'
import { Box, Heading, Text, Button, VStack } from '@chakra-ui/react'
import { useAuth } from '../context/AuthContext.jsx'

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
  beforeLoad: ({ context }) => {
    // If user is not authenticated, redirect to login
    if (!context.auth?.user) {
      throw redirect({
        to: '/login',
      })
    }
  },
})

function DashboardPage() {
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <Box p={8}>
      <VStack spacing={6} align="start">
        <Heading>Dashboard</Heading>
        <Text>Welcome, {user?.email}!</Text>
        <Text>You are successfully logged in with Supabase authentication.</Text>
        <Button colorScheme="red" onClick={handleSignOut}>
          Sign Out
        </Button>
      </VStack>
    </Box>
  )
}
