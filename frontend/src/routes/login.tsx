import { createFileRoute, redirect } from '@tanstack/react-router'
import { LoginForm } from '../components/LoginForm.jsx'

export const Route = createFileRoute('/login')({
  component: LoginPage,
  beforeLoad: ({ context }) => {
    // If user is already authenticated, redirect to dashboard
    if (context.auth?.user) {
      throw redirect({
        to: '/dashboard',
      })
    }
  },
})

function LoginPage() {
  return <LoginForm />
}
