import { describe, expect, test } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ChakraProvider } from '@chakra-ui/react'
import { system } from './theme'
import App from './App.tsx'

// Wrapper component to provide Chakra UI context
const ChakraWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChakraProvider value={system}>
    {children}
  </ChakraProvider>
)

describe('App', () => {
  test('renders step indicator with Chakra UI Steps component', () => {
    render(
      <ChakraWrapper>
        <App />
      </ChakraWrapper>
    )

    // Check if the steps component is rendered (step indicators should be visible)
    const stepIndicators = screen.getAllByText(/^[1-3]$/)
    expect(stepIndicators).toHaveLength(3)
  })

  test('shows upload section on step 1', () => {
    render(
      <ChakraWrapper>
        <App />
      </ChakraWrapper>
    )

    // Check if upload section is visible
    expect(screen.getByText('Upload Your Space Photo')).toBeDefined()
    expect(screen.getByText('Choose Photo')).toBeDefined()
  })

  test('shows pro tips card on step 1', () => {
    render(
      <ChakraWrapper>
        <App />
      </ChakraWrapper>
    )

    // Check if Pro Tips card is visible
    expect(screen.getByText('Pro Tips for Best Results')).toBeDefined()
    expect(screen.getByText('Perfect Lighting')).toBeDefined()
    expect(screen.getByText('Complete View')).toBeDefined()
    expect(screen.getByText('High Resolution')).toBeDefined()
    expect(screen.getByText('Straight Angle')).toBeDefined()
  })

  test('step indicator shows correct active step', () => {
    render(
      <ChakraWrapper>
        <App />
      </ChakraWrapper>
    )

    // Check if step indicators are rendered
    const stepIndicators = screen.getAllByText(/^[1-3]$/)
    expect(stepIndicators).toHaveLength(3)

    // Should start on step 1 (Upload Photo)
    expect(screen.getByText('Upload Your Space Photo')).toBeDefined()
  })

  test('uses custom Google Fonts from theme', () => {
    render(
      <ChakraWrapper>
        <App />
      </ChakraWrapper>
    )

    // Check that heading text uses Playfair Display (should be h3 element)
    const heading = screen.getByRole('heading', { name: 'Upload Your Space Photo' })
    expect(heading).toBeDefined()
    expect(heading.tagName).toBe('H3')

    // Check that body text elements are present
    const bodyText = screen.getByText('Submit a clear photo of the space to redesign')
    expect(bodyText).toBeDefined()
  })

  test('shows Quick Tips button and drawer functionality', () => {
    render(
      <ChakraWrapper>
        <App />
      </ChakraWrapper>
    )

    // Check if Quick Tips button is visible
    const quickTipsButton = screen.getByText('Quick Tips')
    expect(quickTipsButton).toBeDefined()

    // Check that drawer content is not initially visible (drawer should be closed)
    expect(screen.queryByText('Good Lighting')).toBeNull()
    expect(screen.queryByText('Complete View')).toBeNull()
    expect(screen.queryByText('High Resolution')).toBeNull()
    expect(screen.queryByText('Straight Angle')).toBeNull()

    // Check that tip images are not visible when drawer is closed
    expect(screen.queryByAltText('Good lighting example')).toBeNull()
    expect(screen.queryByAltText('Bad lighting example')).toBeNull()
  })
})
