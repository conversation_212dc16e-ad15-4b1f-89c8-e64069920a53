import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import { ChakraProvider, Box } from '@chakra-ui/react'
import {
  Outlet,
  RouterProvider,
  createRootRoute,
  createRoute,
  createRouter,
} from '@tanstack/react-router'

import { system } from './theme.ts'
import './styles.css'
import reportWebVitals from './reportWebVitals.ts'
import { AuthProvider } from './context/AuthContext.jsx'
import { LoginForm } from './components/LoginForm.jsx'
import { ProtectedRoute } from './components/ProtectedRoute.jsx'

import App from './App.tsx'

const rootRoute = createRootRoute({
  component: RootComponent,
})

function RootComponent() {
  return (
    <>
      <div style={{ padding: '20px', backgroundColor: 'lightblue' }}>
        <h1>App is working!</h1>
        <p>This is the root component.</p>
      </div>
      <Outlet />
    </>
  )
}



const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: IndexPage,
})

const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: LoginPage,
})

const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/dashboard',
  component: DashboardPage,
})

function IndexPage() {
  return <App />
}

function LoginPage() {
  return <LoginForm />
}

function DashboardPage() {
  return (
    <ProtectedRoute>
      <Box p={8}>
        <h1>Dashboard</h1>
        <p>Welcome to your dashboard!</p>
      </Box>
    </ProtectedRoute>
  )
}

const routeTree = rootRoute.addChildren([indexRoute, loginRoute, dashboardRoute])

const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPreloadStaleTime: 0,
})

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('app')
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <ChakraProvider value={system}>
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
      </ChakraProvider>
    </StrictMode>,
  )
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals()
