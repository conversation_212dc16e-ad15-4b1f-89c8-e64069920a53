import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import App from './App';
import theme from './theme';

// Mock environment variable
const mockEnv = vi.hoisted(() => ({
  VITE_DRY_MODE: undefined as string | undefined,
}));

vi.mock('import.meta', () => ({
  env: mockEnv,
}));

// Mock fetch for non-dry mode tests
global.fetch = vi.fn();

const renderApp = () => {
  return render(
    <ChakraProvider value={theme}>
      <App />
    </ChakraProvider>
  );
};

describe('DRY_MODE functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockEnv.VITE_DRY_MODE = undefined;
  });

  it('should use input image as mock response when DRY_MODE is enabled', async () => {
    // Enable dry mode
    mockEnv.VITE_DRY_MODE = 'true';

    // Mock console.log to verify dry mode message
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

    renderApp();

    // Upload a file first
    const fileInput = screen.getByLabelText(/upload/i);
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    fireEvent.change(fileInput, { target: { files: [file] } });

    // Wait for file to be processed and move to style selection
    await waitFor(() => {
      expect(screen.getByText(/select a style/i)).toBeInTheDocument();
    });

    // Select a style
    const styleButton = screen.getAllByRole('button')[0]; // First style button
    fireEvent.click(styleButton);

    // Move to review step and generate design
    const generateButton = screen.getByText(/generate design/i);
    fireEvent.click(generateButton);

    // Verify dry mode console message appears
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('DRY_MODE enabled - using mock response')
      );
    });

    // Verify fetch was not called in dry mode (input image is used instead)
    expect(fetch).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });

  it('should make API call when DRY_MODE is disabled', async () => {
    // Dry mode is disabled (default)
    mockEnv.VITE_DRY_MODE = undefined;

    // Mock successful API response
    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ redesignedImageUrl: 'data:image/png;base64,test' }),
    });

    renderApp();

    // Upload a file first
    const fileInput = screen.getByLabelText(/upload/i);
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    fireEvent.change(fileInput, { target: { files: [file] } });

    // Wait for file to be processed and move to style selection
    await waitFor(() => {
      expect(screen.getByText(/select a style/i)).toBeInTheDocument();
    });

    // Select a style
    const styleButton = screen.getAllByRole('button')[0]; // First style button
    fireEvent.click(styleButton);

    // Move to review step and generate design
    const generateButton = screen.getByText(/generate design/i);
    fireEvent.click(generateButton);

    // Verify API call was made
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        'http://127.0.0.1:5000/process-image',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        })
      );
    });
  });

  it('should handle different truthy values for DRY_MODE', () => {
    const testValues = ['true', '1', 'yes', 'on'];

    testValues.forEach(value => {
      mockEnv.VITE_DRY_MODE = value;

      // Test that the condition evaluates to truthy
      const isDryMode = mockEnv.VITE_DRY_MODE;
      expect(isDryMode).toBeTruthy();
    });
  });
});
