import { <PERSON>, <PERSON><PERSON>, Card, <PERSON>Button, Drawer, HStack, Icon, Image, Portal, Text, VStack } from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON><PERSON>, LuX } from 'react-icons/lu';

interface QuickTipsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function QuickTipsDrawer({ isOpen, onClose }: QuickTipsDrawerProps) {
  return (
    <Drawer.Root
      open={isOpen}
      onOpenChange={(e) => !e.open && onClose()}
      placement="bottom"
      size="lg"
    >
      <Portal>
        <Drawer.Backdrop />
        <Drawer.Positioner>
          <Drawer.Content>
            <Drawer.Header>
              <Drawer.CloseTrigger asChild pos="initial">
                <CloseButton />
              </Drawer.CloseTrigger>
              <Drawer.Title flex="1">Quick Tips</Drawer.Title>
            </Drawer.Header>

            <Drawer.Body>
              <VStack gap={4} align="stretch">
                {/* Good Lighting */}
                <Card.Root variant="outline" bg="white">
                  <Card.Body p={4}>
                    <Text fontSize="md" fontWeight="medium" mb={3} color="gray.800">
                      Good Lighting
                    </Text>
                    <HStack gap={3} align="flex-start">
                      <Box
                        flex={1}
                        position="relative"
                      >
                        <Image
                          src="/images/photo_tips/lightning_yes.png"
                          alt="Good lighting example"
                          aspectRatio="4/3"
                          objectFit="cover"
                          rounded="md"
                          w="full"
                        />
                        <Box
                          position="absolute"
                          top={2}
                          right={2}
                          w={5}
                          h={5}
                          bg="green.500"
                          rounded="full"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon color="white" boxSize={3}>
                            <LuCheck />
                          </Icon>
                        </Box>
                      </Box>
                      <Box
                        flex={1}
                        position="relative"
                      >
                        <Image
                          src="/images/photo_tips/lightning_no.png"
                          alt="Bad lighting example"
                          aspectRatio="4/3"
                          objectFit="cover"
                          rounded="md"
                          w="full"
                        />
                        <Box
                          position="absolute"
                          top={2}
                          right={2}
                          w={5}
                          h={5}
                          bg="red.500"
                          rounded="full"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon color="white" boxSize={3}>
                            <LuX />
                          </Icon>
                        </Box>
                      </Box>
                    </HStack>
                  </Card.Body>
                </Card.Root>

                {/* Complete View */}
                <Card.Root variant="outline" bg="white">
                  <Card.Body p={4}>
                    <Text fontSize="md" fontWeight="medium" mb={3} color="gray.800">
                      Complete View
                    </Text>
                    <HStack gap={3} align="flex-start">
                      <Box
                        flex={1}
                        position="relative"
                      >
                        <Image
                          src="/images/photo_tips/complete_view_yes.png"
                          alt="Complete view example"
                          aspectRatio="4/3"
                          objectFit="cover"
                          rounded="md"
                          w="full"
                        />
                        <Box
                          position="absolute"
                          top={2}
                          right={2}
                          w={5}
                          h={5}
                          bg="green.500"
                          rounded="full"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon color="white" boxSize={3}>
                            <LuCheck />
                          </Icon>
                        </Box>
                      </Box>
                      <Box
                        flex={1}
                        position="relative"
                      >
                        <Image
                          src="/images/photo_tips/complete_view_no.png"
                          alt="Incomplete view example"
                          aspectRatio="4/3"
                          objectFit="cover"
                          rounded="md"
                          w="full"
                        />
                        <Box
                          position="absolute"
                          top={2}
                          right={2}
                          w={5}
                          h={5}
                          bg="red.500"
                          rounded="full"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon color="white" boxSize={3}>
                            <LuX />
                          </Icon>
                        </Box>
                      </Box>
                    </HStack>
                  </Card.Body>
                </Card.Root>

                {/* Straight Angle */}
                <Card.Root variant="outline" bg="white">
                  <Card.Body p={4}>
                    <Text fontSize="md" fontWeight="medium" mb={3} color="gray.800">
                      Straight Angle
                    </Text>
                    <HStack gap={3} align="flex-start">
                      <Box
                        flex={1}
                        position="relative"
                      >
                        <Image
                          src="/images/photo_tips/straight_yes.png"
                          alt="Straight angle example"
                          aspectRatio="4/3"
                          objectFit="cover"
                          rounded="md"
                          w="full"
                        />
                        <Box
                          position="absolute"
                          top={2}
                          right={2}
                          w={5}
                          h={5}
                          bg="green.500"
                          rounded="full"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon color="white" boxSize={3}>
                            <LuCheck />
                          </Icon>
                        </Box>
                      </Box>
                      <Box
                        flex={1}
                        position="relative"
                      >
                        <Image
                          src="/images/photo_tips/straight_no.png"
                          alt="Crooked angle example"
                          aspectRatio="4/3"
                          objectFit="cover"
                          rounded="md"
                          w="full"
                        />
                        <Box
                          position="absolute"
                          top={2}
                          right={2}
                          w={5}
                          h={5}
                          bg="red.500"
                          rounded="full"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon color="white" boxSize={3}>
                            <LuX />
                          </Icon>
                        </Box>
                      </Box>
                    </HStack>
                  </Card.Body>
                </Card.Root>
              </VStack>
            </Drawer.Body>
            <Drawer.Footer>
              <Button onClick={onClose}>Got it</Button>
            </Drawer.Footer>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
