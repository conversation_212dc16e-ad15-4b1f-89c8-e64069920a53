import React from 'react'
import { <PERSON>, Spinner, Center } from '@chakra-ui/react'
import { useAuth } from '../context/AuthContext.jsx'
import { LoginForm } from './LoginForm.jsx'

export const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Center h="100vh">
        <Spinner size="xl" />
      </Center>
    )
  }

  if (!user) {
    return <LoginForm />
  }

  return children
}
