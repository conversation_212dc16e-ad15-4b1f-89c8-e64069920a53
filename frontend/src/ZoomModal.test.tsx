import { describe, expect, test, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ChakraProvider } from '@chakra-ui/react'
import { system } from './theme'
import App from './App.tsx'

// Wrapper component to provide Chakra UI context
const ChakraWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChakraProvider value={system}>
    {children}
  </ChakraProvider>
)

// Mock file for testing
const createMockFile = (name: string, type: string) => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: 1024 })
  return file
}

// Helper to simulate file upload and navigate to results
const navigateToResults = async () => {
  const app = render(
    <ChakraWrapper>
      <App />
    </ChakraWrapper>
  )

  // Upload a file
  const fileInput = screen.getByRole('textbox', { hidden: true }) as HTMLInputElement
  const mockFile = createMockFile('test-image.jpg', 'image/jpeg')

  fireEvent.change(fileInput, { target: { files: [mockFile] } })

  // Continue to style selection
  const continueButton = screen.getByText('Continue →')
  fireEvent.click(continueButton)

  // Select a style (first one)
  const styleCards = screen.getAllByRole('img')
  fireEvent.click(styleCards[0])

  // Generate design (this will use dry mode if enabled)
  const generateButton = screen.getByText('Generate Design →')
  fireEvent.click(generateButton)

  // Wait for the results to appear
  await screen.findByText('Your Redesigned Space')

  return app
}

describe('Zoom Modal Functionality', () => {
  test('shows zoom buttons on before/after images in results screen', async () => {
    await navigateToResults()

    // Check if zoom buttons are present by looking for the zoom icons
    const zoomIcons = screen.getAllByRole('button').filter(button =>
      button.querySelector('svg') && button.style.position === 'absolute'
    )
    expect(zoomIcons.length).toBeGreaterThanOrEqual(2) // One for original, one for redesigned
  })

  test('opens zoom modal when image is clicked', async () => {
    await navigateToResults()

    // Click anywhere on the first image (original image)
    const originalImage = screen.getByAltText('Before')
    fireEvent.click(originalImage)

    // Check if close button is present
    expect(screen.getByRole('button', { name: /close/i })).toBeDefined()

    // Check if the zoomed image is displayed
    const zoomedImage = screen.getByAltText('Original space photo')
    expect(zoomedImage).toBeDefined()
  })

  test('closes zoom modal when close button is clicked', async () => {
    await navigateToResults()

    // Open zoom modal by clicking on image
    const originalImage = screen.getByAltText('Before')
    fireEvent.click(originalImage)

    // Verify modal is open by checking for zoomed image
    expect(screen.getByAltText('Original space photo')).toBeDefined()

    // Close modal
    const closeButton = screen.getByRole('button', { name: /close/i })
    fireEvent.click(closeButton)

    // Verify modal is closed (zoomed image should not be visible)
    expect(screen.queryByAltText('Original space photo')).toBeNull()
  })

  test('shows correct image for redesigned image zoom', async () => {
    await navigateToResults()

    // Click on the redesigned image (After image)
    const redesignedImage = screen.getByAltText('After')
    fireEvent.click(redesignedImage)

    // Check if modal is opened with correct image for redesigned space
    expect(screen.getByAltText('Redesigned space')).toBeDefined()
  })

  test('zoom buttons have magnifying glass icons', async () => {
    await navigateToResults()

    // Check if zoom buttons contain the zoom icon (now they are icon-only buttons)
    const zoomButtons = screen.getAllByRole('button').filter(button =>
      button.querySelector('svg') && button.style.position === 'absolute'
    )

    // Each zoom button should have the LuZoomIn icon
    zoomButtons.forEach(button => {
      expect(button).toBeDefined()
      // The icon should be present as an SVG element
      const icon = button.querySelector('svg')
      expect(icon).toBeDefined()
    })
  })

  test('closes zoom modal when clicking outside the image', async () => {
    await navigateToResults()

    // Open zoom modal by clicking on image
    const originalImage = screen.getByAltText('Before')
    fireEvent.click(originalImage)

    // Verify modal is open
    expect(screen.getByAltText('Original space photo')).toBeDefined()

    // Click outside the image (on the backdrop)
    const backdrop = screen.getByRole('dialog').parentElement
    if (backdrop) {
      fireEvent.click(backdrop)
    }

    // Verify modal is closed
    expect(screen.queryByAltText('Original space photo')).toBeNull()
  })
})
