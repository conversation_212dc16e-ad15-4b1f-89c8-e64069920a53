import { createSystem, defaultConfig } from "@chakra-ui/react";

// Custom theme configuration with Google Fonts
const customConfig = {
  ...defaultConfig,
  theme: {
    ...defaultConfig.theme,
    tokens: {
      ...defaultConfig.theme?.tokens,
      fonts: {
        heading: { value: `'Playfair Display', serif` },
        body: { value: `'Geist', sans-serif` },
      },
    },
    slotRecipes: {
      ...defaultConfig.theme?.slotRecipes,
      card: {
        ...defaultConfig.theme?.slotRecipes?.card,
        slots: ["root", "header", "body", "footer", "title", "description"],
        base: {
          ...defaultConfig.theme?.slotRecipes?.card?.base,
          title: {
            fontFamily: "heading",
          },
        },
      },
      dialog: {
        ...defaultConfig.theme?.slotRecipes?.dialog,
        slots: ["backdrop", "positioner", "content", "header", "title", "description", "body", "footer", "closeTrigger"],
        base: {
          ...defaultConfig.theme?.slotRecipes?.dialog?.base,
          title: {
            fontFamily: "heading",
            fontSize: "xl",
          },
        },
      },
    },
  },
};

export const system = createSystem(customConfig);
