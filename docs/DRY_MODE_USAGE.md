# DRY_MODE Implementation Guide

## Overview

The `DRY_MODE` environment variable has been implemented to allow frontend testing without making actual API calls to the image generation backend, avoiding LLM charges during development.

## How It Works

When `DRY_MODE` is enabled, the application:
1. Skips the actual API call to `http://127.0.0.1:5000/process-image`
2. Returns the uploaded input image as if it were the transformed result
3. Maintains the same loading states and user experience
4. Simulates a 2-second delay to mimic real API response times

## Usage

### Method 1: Using the Convenient Script
```bash
yarn run dev:dry
```

### Method 2: Setting Environment Variable Manually
```bash
VITE_DRY_MODE=true yarn run dev
```

### Method 3: Using Different Truthy Values
Any truthy value will enable dry mode:
```bash
VITE_DRY_MODE=1 yarn run dev
VITE_DRY_MODE=yes yarn run dev
VITE_DRY_MODE=on yarn run dev
```

## Implementation Details

### Files Modified
- `src/App.tsx`: Added dry mode logic to `handleGenerateDesign` function
- `package.json`: Added `dev:dry` script for convenience

### Key Functions Added

1. **`getMockResponse()`**: Returns the uploaded input image as the mock transformed result
2. **Dry mode check**: Uses `import.meta.env.VITE_DRY_MODE` to determine if dry mode is enabled

### Mock Response Format
The mock response maintains the same structure as the real API:
```typescript
{
  redesignedImageUrl: string // Base64 data URL of the input image
}
```

### How It Works
Instead of using a static mock image, the system returns the user's uploaded image as the "transformed" result. This provides a more realistic testing experience since you can see how the UI handles different image types and sizes that you actually upload.

## Console Output
When dry mode is active, you'll see this message in the browser console:
```
🔧 DRY_MODE enabled - using mock response instead of API call
```

## Testing the Implementation

1. Start the application in dry mode:
   ```bash
   yarn run dev:dry
   ```

2. Upload an image and select a style

3. Click "Generate Design" - you should see:
   - The loading state for ~2 seconds
   - The console message indicating dry mode is active
   - Your uploaded image displayed as the "transformed" result
   - No network requests to the backend API

## Benefits

- **Cost Savings**: Avoid LLM API charges during frontend development
- **Faster Development**: No need to run the backend service for UI testing
- **Realistic Testing**: Test with actual uploaded images of different types and sizes
- **Offline Development**: Work on the frontend without backend dependencies

## Normal Mode

To return to normal API mode, simply run:
```bash
yarn run dev
```

This will make actual API calls to the backend service as usual.
