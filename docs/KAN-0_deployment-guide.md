# Production Deployment Guide

This guide covers deploying the Interior Designer application with Clerk authentication to production.

## Prerequisites

- Completed Clerk setup (see clerk-setup-guide.md)
- Production domain with SSL certificate
- Hosting platform account (Vercel, Netlify, DigitalOcean, etc.)

## 1. Environment Configuration

### Frontend Environment Variables

Create production environment variables:

```bash
# Production .env
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_production_publishable_key
VITE_API_URL=https://api.yourdomain.com
```

### Backend Environment Variables

```bash
# Production .env
CLERK_SECRET_KEY=sk_live_your_production_secret_key
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_production_publishable_key
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=production
```

## 2. Clerk Production Configuration

### Switch to Production Instance

1. In Clerk Dashboard, switch from Development to Production
2. Update all environment variables to use production keys
3. Configure production domains and redirect URLs

### Production Domains

In Clerk Dashboard → Domains:
- Add your production domain: `yourdomain.com`
- Add API domain: `api.yourdomain.com`

### Production Redirect URLs

Update all redirect URLs in Clerk Dashboard → Paths:
- **Sign-in URL:** `https://yourdomain.com/sign-in`
- **Sign-up URL:** `https://yourdomain.com/sign-up`
- **After sign-in URL:** `https://yourdomain.com/`
- **After sign-up URL:** `https://yourdomain.com/`
- **After sign-out URL:** `https://yourdomain.com/`

## 3. OAuth Provider Configuration

### Google OAuth Production

Update Google Cloud Console OAuth settings:
- **Authorized JavaScript origins:** `https://yourdomain.com`
- **Authorized redirect URIs:**
  - `https://your-production-clerk-domain.clerk.accounts.dev/v1/oauth_callback`

### Apple Sign-In Production

Update Apple Developer Portal settings:
- **Domains:** `your-production-clerk-domain.clerk.accounts.dev`
- **Redirect URLs:** `https://your-production-clerk-domain.clerk.accounts.dev/v1/oauth_callback`

## 4. Frontend Deployment

### Vercel Deployment

1. **Connect Repository:**
   ```bash
   npm install -g vercel
   vercel login
   vercel --prod
   ```

2. **Environment Variables:**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add `VITE_CLERK_PUBLISHABLE_KEY`
   - Add `VITE_API_URL`

3. **Build Settings:**
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

### Netlify Deployment

1. **Connect Repository:**
   - Go to Netlify Dashboard
   - Click "New site from Git"
   - Connect your repository

2. **Build Settings:**
   - Build command: `npm run build`
   - Publish directory: `dist`

3. **Environment Variables:**
   - Go to Site Settings → Environment Variables
   - Add `VITE_CLERK_PUBLISHABLE_KEY`
   - Add `VITE_API_URL`

## 5. Backend Deployment

### DigitalOcean App Platform

1. **Create App:**
   ```yaml
   # .do/app.yaml
   name: interior-designer-api
   services:
   - name: api
     source_dir: /backend
     github:
       repo: your-username/web-interior-designer
       branch: main
     run_command: python main.py
     environment_slug: python
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: CLERK_SECRET_KEY
       value: sk_live_your_production_secret_key
       type: SECRET
     - key: VITE_CLERK_PUBLISHABLE_KEY
       value: pk_live_your_production_publishable_key
     - key: API_HOST
       value: 0.0.0.0
     - key: API_PORT
       value: 8000
     - key: ENVIRONMENT
       value: production
   ```

2. **Deploy:**
   ```bash
   doctl apps create --spec .do/app.yaml
   ```

### Docker Deployment

1. **Create Dockerfile:**
   ```dockerfile
   # backend/Dockerfile
   FROM python:3.9-slim

   WORKDIR /app

   COPY requirements.txt .
   RUN pip install -r requirements.txt

   COPY . .

   EXPOSE 8000

   CMD ["python", "main.py"]
   ```

2. **Build and Deploy:**
   ```bash
   docker build -t interior-designer-api ./backend
   docker run -p 8000:8000 --env-file .env interior-designer-api
   ```

## 6. Domain and SSL Configuration

### Custom Domain Setup

1. **Configure DNS:**
   - Point `yourdomain.com` to frontend hosting
   - Point `api.yourdomain.com` to backend hosting

2. **SSL Certificates:**
   - Most hosting platforms provide automatic SSL
   - Ensure HTTPS is enforced

### CORS Configuration

Update backend CORS settings for production:

```python
# backend/main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Production domain only
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

## 7. Monitoring and Logging

### Application Monitoring

1. **DigitalOcean Monitoring:**
   ```bash
   doctl apps logs <app-id> --deployment <deployment-id> --type build --tail 20
   ```

2. **Health Check Endpoint:**
   - Monitor `/api/health` endpoint
   - Set up uptime monitoring

### Error Tracking

Consider integrating error tracking services:
- Sentry
- LogRocket
- Bugsnag

## 8. Security Checklist

### Production Security

- [ ] HTTPS enforced on all domains
- [ ] Environment variables secured
- [ ] CORS configured for production domains only
- [ ] Rate limiting implemented
- [ ] Security headers configured
- [ ] Regular security updates scheduled

### Clerk Security

- [ ] Production keys rotated from development
- [ ] JWT templates configured
- [ ] Session timeouts configured
- [ ] Multi-factor authentication enabled (optional)

## 9. Performance Optimization

### Frontend Optimization

- [ ] Code splitting implemented
- [ ] Assets optimized and compressed
- [ ] CDN configured for static assets
- [ ] Caching headers configured

### Backend Optimization

- [ ] Database connection pooling
- [ ] API response caching
- [ ] Load balancing configured
- [ ] Auto-scaling enabled

## 10. Backup and Recovery

### Data Backup

- [ ] Database backups automated
- [ ] Environment variables backed up securely
- [ ] Code repository backed up

### Disaster Recovery

- [ ] Recovery procedures documented
- [ ] Backup restoration tested
- [ ] Monitoring and alerting configured

## 11. Post-Deployment Testing

### Authentication Testing

Test all authentication flows in production:
- [ ] Email magic link authentication
- [ ] Google OAuth (if configured)
- [ ] Apple Sign-In (if configured)
- [ ] Session persistence
- [ ] Sign-out functionality

### API Testing

- [ ] All endpoints respond correctly
- [ ] Authentication middleware working
- [ ] CORS configured properly
- [ ] Error handling working

### Performance Testing

- [ ] Page load times acceptable
- [ ] API response times acceptable
- [ ] Authentication flows performant

## 12. Maintenance

### Regular Tasks

- [ ] Monitor application logs
- [ ] Update dependencies regularly
- [ ] Rotate API keys periodically
- [ ] Review security settings
- [ ] Monitor performance metrics

### Scaling Considerations

- [ ] Monitor resource usage
- [ ] Plan for traffic growth
- [ ] Consider database scaling
- [ ] Implement caching strategies

## Support and Resources

- [Clerk Production Checklist](https://clerk.com/docs/deployments/production-checklist)
- [Vercel Deployment Guide](https://vercel.com/docs)
- [DigitalOcean App Platform](https://docs.digitalocean.com/products/app-platform/)
- [Netlify Deployment](https://docs.netlify.com/)
