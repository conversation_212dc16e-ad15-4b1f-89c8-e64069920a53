# Clerk Authentication Setup Guide

This guide walks you through configuring Clerk authentication for the Interior Designer application with Google OAuth, Apple Sign-In, and passwordless email authentication.

## Prerequisites

- Clerk account created at [clerk.com](https://clerk.com)
- Application created in Clerk dashboard
- Clerk publishable key added to environment variables

## 1. Basic Clerk Configuration

### Environment Variables

**Frontend (.env):**
```
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
```

**Backend (.env):**
```
CLERK_SECRET_KEY=sk_test_your_secret_key_here
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
```

## 2. Configure Authentication Methods

### Email (Passwordless Magic Links)

1. Go to Clerk Dashboard → User & Authentication → Email, Phone, Username
2. Enable "Email address" as an identifier
3. Go to User & Authentication → Email & SMS
4. Enable "Email verification"
5. Choose "Email verification link" as the verification method

### Google OAuth

1. **Set up Google Cloud Console:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Go to Credentials → Create Credentials → OAuth 2.0 Client IDs
   - Set Application type to "Web application"
   - Add authorized redirect URIs:
     - `https://your-clerk-domain.clerk.accounts.dev/v1/oauth_callback`
     - For development: `https://balanced-cat-82.clerk.accounts.dev/v1/oauth_callback`
   - Copy Client ID and Client Secret

2. **Configure in Clerk Dashboard:**
   - Go to User & Authentication → Social Connections
   - Click "Add connection" → Google
   - Enter your Google Client ID and Client Secret
   - Enable the connection

### Apple Sign-In

1. **Set up Apple Developer Account:**
   - Go to [Apple Developer Portal](https://developer.apple.com/)
   - Create an App ID with Sign In with Apple capability
   - Create a Service ID for web authentication
   - Configure domains and redirect URLs:
     - Domain: `your-clerk-domain.clerk.accounts.dev`
     - Redirect URL: `https://your-clerk-domain.clerk.accounts.dev/v1/oauth_callback`
   - Create a private key for Sign In with Apple
   - Download the key file (.p8)

2. **Configure in Clerk Dashboard:**
   - Go to User & Authentication → Social Connections
   - Click "Add connection" → Apple
   - Enter your Apple Team ID, Service ID, Key ID, and upload the private key
   - Enable the connection

## 3. Configure Redirect URLs

### Development Environment

In Clerk Dashboard → Paths:
- **Sign-in URL:** `/sign-in`
- **Sign-up URL:** `/sign-up`
- **After sign-in URL:** `/`
- **After sign-up URL:** `/`
- **After sign-out URL:** `/`

### Production Environment

Update the URLs to match your production domain:
- **Sign-in URL:** `https://yourdomain.com/sign-in`
- **Sign-up URL:** `https://yourdomain.com/sign-up`
- **After sign-in URL:** `https://yourdomain.com/`
- **After sign-up URL:** `https://yourdomain.com/`
- **After sign-out URL:** `https://yourdomain.com/`

## 4. Security Configuration

### Session Settings

In Clerk Dashboard → Sessions:
- **Session lifetime:** 7 days (recommended)
- **Inactivity timeout:** 30 minutes (recommended)
- **Multi-session handling:** Allow multiple sessions

### JWT Template (for Backend)

In Clerk Dashboard → JWT Templates:
1. Create a new template named "default"
2. Add custom claims if needed:
   ```json
   {
     "user_id": "{{user.id}}",
     "email": "{{user.primary_email_address.email_address}}",
     "email_verified": "{{user.primary_email_address.verification.status == 'verified'}}",
     "given_name": "{{user.first_name}}",
     "family_name": "{{user.last_name}}",
     "name": "{{user.full_name}}"
   }
   ```

## 5. Testing Authentication Flows

### Manual Testing Checklist

- [ ] **Email Magic Link:**
  - Sign up with email
  - Receive and click magic link
  - Successfully authenticated

- [ ] **Google OAuth:**
  - Click "Sign in with Google"
  - Redirected to Google consent screen
  - Grant permissions
  - Redirected back to app and authenticated

- [ ] **Apple Sign-In:**
  - Click "Sign in with Apple" (test in Safari)
  - Redirected to Apple consent screen
  - Grant permissions
  - Redirected back to app and authenticated

- [ ] **Session Persistence:**
  - Sign in
  - Refresh browser
  - Still authenticated

- [ ] **Sign Out:**
  - Click sign out
  - Redirected to public page
  - Cannot access protected routes

## 6. Production Deployment

### Environment Variables

Ensure these are set in your production environment:
- `VITE_CLERK_PUBLISHABLE_KEY` (frontend)
- `CLERK_SECRET_KEY` (backend)

### HTTPS Requirements

- Clerk requires HTTPS in production
- Ensure your domain has a valid SSL certificate
- Update all redirect URLs to use HTTPS

### Domain Configuration

1. Add your production domain to Clerk Dashboard → Domains
2. Update all redirect URLs to use your production domain
3. Update OAuth provider redirect URLs to use your production domain

## 7. Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error:**
   - Check that redirect URLs match exactly in OAuth provider settings
   - Ensure URLs use HTTPS in production

2. **"Invalid publishable key" error:**
   - Verify environment variable is set correctly
   - Check for extra spaces or characters

3. **JWT verification fails:**
   - Ensure CLERK_SECRET_KEY is set in backend
   - Check that JWT template is configured correctly

4. **OAuth provider not showing:**
   - Verify the connection is enabled in Clerk Dashboard
   - Check that OAuth provider is properly configured

### Support

- [Clerk Documentation](https://clerk.com/docs)
- [Clerk Discord Community](https://discord.com/invite/b5rXHjAg7A)
- [GitHub Issues](https://github.com/clerkinc/clerk-react/issues)

## 8. Security Best Practices

- Never commit secret keys to version control
- Use environment variables for all sensitive configuration
- Regularly rotate API keys and secrets
- Monitor authentication logs for suspicious activity
- Keep Clerk SDK updated to latest version
- Use HTTPS in production
- Configure proper CORS settings
- Implement rate limiting on authentication endpoints
