# Hello World Deployment for Web Interior Designer

This is a simplified "Hello World" version of the Web Interior Designer application, created to verify the deployment pipeline to DigitalOcean before implementing the full application.

## What's Included

1. **Simplified Backend** (`backend/hello_world.py`):
   - A minimal FastAPI application with two endpoints:
     - `/` - Returns a simple greeting
     - `/api/health` - Returns the health status of the API

2. **Simplified Frontend** (`frontend/src/HelloWorld.tsx`):
   - A basic React component that displays a greeting and checks the API connection

3. **Deployment Configuration** (`app.hello.yaml`):
   - DigitalOcean App Platform configuration for the Hello World version

## How to Deploy

1. **Update the GitHub Repository**:
   Open `app.hello.yaml` and replace `your/repo` with your actual GitHub repository.

2. **Build and Push the Docker Image**:
   ```bash
   # Login to DigitalOcean Container Registry
   doctl registry login
   
   # Build the Docker image
   docker build -t registry.digitalocean.com/YOUR_PROJECT/api-hello:v1 -f Dockerfile.hello .
   
   # Push the Docker image
   docker push registry.digitalocean.com/YOUR_PROJECT/api-hello:v1
   ```

3. **Deploy to DigitalOcean App Platform**:
   ```bash
   # Deploy using the app.hello.yaml configuration
   doctl apps create --spec app.hello.yaml
   ```

4. **Verify the Deployment**:
   - Visit the frontend URL provided by DigitalOcean
   - Check that the API health endpoint is working

## Local Development

1. **Run the Backend**:
   ```bash
   # Install FastAPI and Uvicorn
   pip install fastapi uvicorn
   
   # Run the Hello World backend
   python backend/hello_world.py
   ```

2. **Run the Frontend**:
   ```bash
   # Install dependencies
   cd frontend
   npm install
   
   # Run the Hello World frontend
   npm run dev -- --config vite.hello.config.js
   ```

## Next Steps

After successfully deploying this Hello World version, we can proceed with deploying the full Web Interior Designer application as outlined in the deployment plan.